import sys
import flet as ft

# 添加src目录到路径
sys.path.append(".")

from src.Gui.FletGui.welcome_screen import WelcomeScreen

def main(page: ft.Page):
    page.title = "欢迎界面测试"
    page.window.center()

    welcome_screen = WelcomeScreen()

    def on_welcome_complete():
        print("欢迎界面动画完成！")
        # 显示完成消息并等待用户关闭
        page.clean()
        page.add(
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text(
                            "欢迎界面动画完成！",
                            size=24,
                            color=ft.colors.GREEN,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        ft.ElevatedButton(
                            "关闭",
                            on_click=lambda _: page.window.close(),
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                width=1200,
                height=730,
                alignment=ft.alignment.center,
                bgcolor=ft.colors.BLUE_GREY_900,
            )
        )
        page.update()

    welcome_screen.show(page, on_welcome_complete)
    

if __name__ == "__main__":
    ft.app(main)
