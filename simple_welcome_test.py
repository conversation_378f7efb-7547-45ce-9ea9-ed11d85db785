import sys
import flet as ft
import random
import threading
import time

# 添加src目录到路径
sys.path.append(".")

class SimpleWelcomeScreen:
    def __init__(self):
        self.page = None
        self.on_complete_callback = None
        
    def show(self, page: ft.Page, on_complete=None):
        """显示简化版欢迎界面"""
        self.page = page
        self.on_complete_callback = on_complete
        
        # 设置页面属性
        page.window.height = 730
        page.window.width = 1200
        page.theme_mode = ft.ThemeMode.DARK
        page.padding = 0
        page.spacing = 0
        
        # 创建简化的欢迎界面
        welcome_ui = self.create_simple_ui()
        page.add(welcome_ui)
        page.update()
        
        # 启动动画
        self.start_simple_animation()
    
    def create_simple_ui(self):
        """创建简化的UI"""
        # Logo
        logo = ft.Container(
            content=ft.Icon(
                ft.icons.ACCOUNT_BALANCE,
                size=100,
                color=ft.colors.CYAN_400,
            ),
            width=150,
            height=150,
            border_radius=75,
            bgcolor=ft.colors.with_opacity(0.1, ft.colors.CYAN_400),
            border=ft.border.all(2, ft.colors.CYAN_400),
            alignment=ft.alignment.center,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 标题
        title = ft.Text(
            "信小财 RPA 财务机器人",
            size=36,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.WHITE,
            text_align=ft.TextAlign.CENTER,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 副标题
        subtitle = ft.Text(
            "智能化财务处理 • 高效自动化 • 精准可靠",
            size=16,
            color=ft.colors.CYAN_200,
            text_align=ft.TextAlign.CENTER,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 进度条
        progress = ft.ProgressBar(
            width=300,
            height=4,
            bgcolor=ft.colors.with_opacity(0.2, ft.colors.CYAN_400),
            color=ft.colors.CYAN_400,
            value=0,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 加载文本
        loading_text = ft.Text(
            "正在启动...",
            size=14,
            color=ft.colors.CYAN_200,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 存储引用以便动画使用
        self.logo = logo
        self.title = title
        self.subtitle = subtitle
        self.progress = progress
        self.loading_text = loading_text
        
        # 主容器
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Container(height=100),
                    logo,
                    ft.Container(height=30),
                    title,
                    ft.Container(height=15),
                    subtitle,
                    ft.Container(height=60),
                    progress,
                    ft.Container(height=15),
                    loading_text,
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            width=1200,
            height=730,
            alignment=ft.alignment.center,
            gradient=ft.LinearGradient(
                begin=ft.alignment.top_left,
                end=ft.alignment.bottom_right,
                colors=[
                    ft.colors.BLUE_GREY_900,
                    ft.colors.BLUE_GREY_800,
                    ft.colors.INDIGO_900,
                ],
            ),
        )
    
    def start_simple_animation(self):
        """启动简化动画"""
        def animation_sequence():
            # Logo动画
            time.sleep(0.5)
            self.logo.opacity = 1
            if self.page:
                self.page.update()
            
            # 标题动画
            time.sleep(0.8)
            self.title.opacity = 1
            if self.page:
                self.page.update()
            
            # 副标题动画
            time.sleep(0.5)
            self.subtitle.opacity = 1
            if self.page:
                self.page.update()
            
            # 进度条和加载文本
            time.sleep(0.5)
            self.progress.opacity = 1
            self.loading_text.opacity = 1
            if self.page:
                self.page.update()
            
            # 进度条动画
            for i in range(101):
                self.progress.value = i / 100
                if self.page:
                    self.page.update()
                time.sleep(0.02)
            
            time.sleep(1)
            
            # 完成动画
            if self.on_complete_callback:
                self.on_complete_callback()
        
        thread = threading.Thread(target=animation_sequence, daemon=True)
        thread.start()

def main(page: ft.Page):
    page.title = "简化欢迎界面测试"
    page.window.center()
    
    welcome_screen = SimpleWelcomeScreen()
    
    def on_welcome_complete():
        print("欢迎界面动画完成！")
        # 显示完成消息
        page.clean()
        page.add(
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text(
                            "欢迎界面动画完成！",
                            size=24,
                            color=ft.colors.GREEN,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        ft.ElevatedButton(
                            "关闭",
                            on_click=lambda _: page.window.close(),
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                width=1200,
                height=730,
                alignment=ft.alignment.center,
                bgcolor=ft.colors.BLUE_GREY_900,
            )
        )
        page.update()
    
    welcome_screen.show(page, on_welcome_complete)

if __name__ == "__main__":
    ft.app(main)
