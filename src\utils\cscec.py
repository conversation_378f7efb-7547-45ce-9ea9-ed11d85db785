from playwright.sync_api import Play<PERSON>, sync_playwright
from playwright.sync_api import Page,El<PERSON><PERSON><PERSON><PERSON>
from playwright.sync_api import ChromiumBrowserContext
import time
from typing import Union


# 连接已经打开的浏览器

def getContext(port_number: str):
    #with sync_playwright() as playwright:
    playwright = sync_playwright().start()
    browser = playwright.chromium.connect_over_cdp("http://localhost:"+port_number)
    default_context = browser.contexts[0]
    return default_context

def getCscecPage():
    playwright = sync_playwright().start()
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page=switch_to_page(default_context,"中国建筑司库一体化")
    return page

from functools import wraps
def tryDecoration(retry_attempts: int = 5): #自定义一个重试装饰器
    def decoratored(func):
        @wraps(func)
        def wrapped_function(*args, **kwargs):
            retry_attempts=5
            while retry_attempts>0:
                try:
                    func(*args, **kwargs)
                    retry_attempts=-1
                except Exception as e:
                    print(f"retry{retry_attempts}")
                    print(e)
                    retry_attempts=retry_attempts-1  
        return wrapped_function          
    return decoratored

def tryFunc(Func,retry_attempts: int = 5,*args): #自定义一个函数重试器
    tryCount=retry_attempts
    while tryCount>0:
        try:
            Func(*args)
            tryCount=-1
        except Exception as e:
            tryCount=tryCount-1

def switch_to_page(context, title: str = None, url: str = None, retry_attempts: int = 3) -> Page:
    """切换到指定title名称或url的标签页"""
    for attempt in range(retry_attempts):
        for item_page in context.pages:
            try:
                # 确保页面加载完成
                item_page.wait_for_load_state('load')
                
                if title and title in item_page.title():
                    # 激活当前选项卡
                    item_page.bring_to_front()
                    return item_page
                elif url and url in item_page.url:
                    # 激活当前选项卡
                    item_page.bring_to_front()
                    return item_page
            except Exception as e:
                print(f"Error while switching to page on attempt {attempt + 1}: {e}")
    
    print("Not found title or url")
    return context.pages[0]

def changeProjectCscec(page:Page,project_organization,project_name):          
    page.click("//img[@src='https://fip.cscec.com/OSPPortal/images/osp/app/menu/csc/qhzz.png']")
    s="//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input"
    judge=page.locator(s).get_attribute("value")

    page.click("//*[@id='DataSetFieldComboBox1-input']/following-sibling::div[1]") #选择公司同级
    page.get_by_placeholder("请输入查询关键字").fill(project_organization)
    page.locator("//*[text()='组织机构']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div/parent::div").click()
    page.locator("//*[text()='组织机构']/parent::div/parent::div/parent::div//div[text()='"+project_organization+"']").click()
    page.locator("//*[text()='组织机构']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

    page.click("//*[@id='DataSetFieldComboBox3-input']/following-sibling::div[1]") #选择项目同级
    page.get_by_placeholder("请输入查询关键字").fill(project_name)
    page.locator("//*[text()='项目名称']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div/parent::div").click()
    page.locator(f"//*[text()='项目名称']/parent::div/parent::div/parent::div//*[text()='{project_name}']").click()
    page.locator("//*[text()='项目名称']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

    page.click("//*[@id='DataSetFieldComboBox2-input']/following-sibling::div[1]") #选择部门同级
    s="//div[contains(text(),'部门编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//td[1]"
    page.locator(s).click() #XN0+公司编码级次1
    page.locator("//*[text()='部门名称']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

    page.locator("//*[text()='切换组织机构']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()
    try:
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click(timeout=1000)
    except:
        print("无提示")
    #切换项目完成
    
def getInputAbove(page:Page,s):
    if type(s)==str:
        need=getVisible(page,s).bounding_box()
    else:
        need=s.bounding_box()
    x1=need['x']
    y1=need['y']
    x2=need['x'] + need['width']
    y2=need['y'] + need['height']
    inputs=page.locator("//input[@id='undefined-input']")
    count=inputs.count()
    for i in range(0,count):
        try:
            input=inputs.nth(i)
            x3=input.bounding_box()['x']+input.bounding_box()['width']/2
            y3=input.bounding_box()['y']+input.bounding_box()['height']/2
            if x1<x3<x2 and y1<y3<y2 :
                return input
                break
        except:
            i
    
def clickMouse(page:Page,s,x_proportion,y_proportion): #强制点击
    if type(s)==str:
        need=page.locator(s).bounding_box()
    else:
        need=s.bounding_box()
    x=need['x'] + need['width']*x_proportion/100
    y=need['y'] + need['height']*y_proportion/100
    page.mouse.click(x, y) 

def getVisible(page:Page,s:Union[str,ElementHandle],timeout=700):  #
    if type(s)==str:
        while timeout>0:
            ele=page.locator(s)
            for i in range(ele.count()):
                if ele.nth(i).is_visible():
                    timeout=-1
                    return ele.nth(i)
            timeout=timeout-1
            time.sleep(0.1)

    else:
        ele=s
        for i in range(ele.count()):
            if ele.nth(i).is_visible():
                return ele.nth(i)
                

def getTr(page:Page,s:str): #返回表格或body否则连用visible只得到一个tr
    try:
        s="//div[contains(text(),'"+s+"')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]"
        tbody=getVisible(page,s)
        tr=tbody.locator("//tr") #不然visible只有一个tr
        return tr
    except:
        s="//div[contains(text(),'"+s+"')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]"
        table=getVisible(page,s)
        tr=table.locator("//tr")
        return tr


class cscecTable():
    def __init__(self,page:Page,markStr:str) -> None:
        self.page=page
        self.tr=None
        self.title={}
        self._markStr=markStr
        self.titleTd=None
        self.title2={}
        self._getTable__()
        self.count=self.tr.count()
    
        
    def _getTable__(self):
        s2="//div[contains(text(),'"+self._markStr+"')]/parent::span/parent::div/parent::td/parent::tr"
        titleCount=1
        try:
            s="//div[contains(text(),'"+self._markStr+"')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]"
            tbody=getVisible(self.page,s)
            tr=tbody.locator("//tr") #不然visible只有一个tr
            self.titleTd=getVisible(self.page,s2).locator("//td")
            for i in range(self.titleTd.count()): #避免不可见元素影响
                try:
                    if self.titleTd.nth(i).is_visible():
                        theTitle=self.titleTd.nth(i).text_content() #不一定都有标题
                        self.title[theTitle]=titleCount
                        self.title2[titleCount]=theTitle
                        titleCount=titleCount+1
                except:
                    pass
            self.tr=tr
        except Exception as e:
            s="//div[contains(text(),'"+self._markStr+"')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]"
            table=getVisible(self.page,s)
            tr=table.locator("//tr")
            self.titleTd=getVisible(self.page,s2).locator("//td")
            for i in range(self.titleTd.count()):
                try:
                    if self.titleTd.nth(i).is_visible():
                        theTitle=self.titleTd.nth(i).text_content() #不一定都有标题
                        self.title[theTitle]=titleCount
                        self.title2[titleCount]=theTitle
                        titleCount=titleCount+1
                except:
                    pass
            self.tr=tr
    def getValue(self,row:int,titleStr:str):
        value=self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]").text_content()
        return value
    def click(self,row:int,titleStr:str):
        self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]").click()

    def getText(self,row:int,titleStr:str):
        self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]").text_content()
    def doubleClick(self,row:int,titleStr:str):
        self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]").dblclick()
   
    @tryDecoration(retry_attempts=5)
    def fillInput(self,row:int,titleStr:str,inputStr:str):
        element=self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]")
        element.dblclick()
        getInputAbove(self.page,element).fill(inputStr)
        
    @tryDecoration(retry_attempts=5)
    def clickInputQuery(self,row:int,titleStr:str):
        self.doubleClick(row,titleStr)
        time.sleep(0.1)
        getInputAbove(self.page,self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]")).locator("//following-sibling::div[1]").click()
        #clickMouse(self.page,self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]"),97,50),修改为上面语句
    @tryDecoration(retry_attempts=5)
    def clickInputQuery2(self,row:int,titleStr:str):
        self.click(row,titleStr)
        time.sleep(0.15)
        getInputAbove(self.page,self.tr.nth(row-1).locator(f"//td[{self.title[titleStr]}]")).locator("//following-sibling::div[1]").click()
    def appendRow(self):
        self.titleTd.nth(0).click()
        getVisible(self.page,"//span[contains(text(),'增加')]").click()
    
    def reIndex(self): #因为隐藏元素的存在，需要重新计算索引
        count=1
        for i in range(self.tr.locator("//td").count()):
            if self.tr.locator("//td").nth(i).is_visible():
                try:
                    self.title[self.title2[count]]=i+1 #不一定count都存在
                    count=count+1
                except:
                    pass
                
                


        
def closeTab(page:Page):
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']").click()

def toFunction(page:Page,s1:str,s2:str,s3:str):
    page.locator(f"//span[text()='{s1}']/parent::span").click()
    page.locator(f"//span[text()='{s2}']/parent::li").click()
    page.locator(f"//li/span[text()='{s3}']").click()

def clickDigalog(page:Page,s1:str,s2=None,timeWait=700):
    if s2!=None:
        getVisible(page,f"//*[contains(text(),'{s1}')]/parent::div/parent::div/parent::div//*[text()='{s2}']",timeout=timeWait).click()
    else:
        getVisible(page,f"//*[contains(text(),'{s1}')]/parent::div/parent::div/parent::div//*[text()='确定' or text()='是' or text()='确认' or text()='提交']",timeout=timeWait).click()

def locatorDigalog(page:Page,s1:str):
    return getVisible(page,f"//*[contains(text(),'{s1}')]//parent::div/parent::div/parent::div")


def uploadAttachment(page:Page,s:str):
    if s!=None:
        time.sleep(1)
        page.locator("//span[text()='附件']/parent::div").click()
        if getVisible(page,"//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'上传')]",timeout=30)==None: #没有附件窗口
            page.locator("//span[text()='附件']/parent::div").click()
        with page.expect_file_chooser() as fc_info:
            page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'上传')]").click() 
            file_chooser = fc_info.value
            file_chooser.set_files(s.split(";"))
            try:
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click(timeout=300000)  
            except:
                clickMouse(page,"//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']",50,50).click()
                clickMouse(page,"//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']",50,50).click() #两个都是可见的
        page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click()

def importTemplate(page:Page,s:str):
    page.locator("//span[text()='导入']/parent::div/parent::div/parent::div/parent::div").first.click()
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    with page.expect_file_chooser() as fc_info:
        page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[contains(text(),'浏览')]/parent::div/parent::div/parent::div/preceding-sibling::input").click() #点击input才有效果
        file_chooser = fc_info.value
        file_chooser.set_files(s)
        page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[text()='导入']").click() 
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()

def dialogInput(page:Page,s:str):
    getVisible(page,"//*[contains(text(),'帮助')]/parent::div/parent::div/parent::div//input").fill(s)
    getVisible(page,"//*[contains(text(),'帮助')]/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").dblclick()
    getVisible(page,f"//*[contains(text(),'帮助')]/parent::div/parent::div/parent::div//*[contains(text(),'{s}')]").dblclick()

def dialogInput2(page:Page,s:str):
    getVisible(page,"//*[contains(text(),'帮助')]/parent::div/parent::div/parent::div//input").fill(s)
    getVisible(page,"//*[contains(text(),'帮助')]/parent::div/parent::div/parent::div//img[contains(@src,'query_up.png')]").dblclick()
    getVisible(page,f"//*[contains(text(),'{s}')]").dblclick()

def Lablechoose(page:Page,labelName,inputPrompt,leftCorner,fillName):
    s=f"//label[contains(text(),'{labelName}')]/parent::div/following-sibling::div[1]/div/div/div"
    getVisible(page,s).click()
    page.get_by_placeholder(inputPrompt).fill(fillName)
    page.locator(f"//*[text()='{leftCorner}']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.locator(f"//*[text()='{leftCorner}']/parent::div/parent::div/parent::div").get_by_text(fillName,exact=True).first.dblclick() 


def fillLalbel_input(page:Page,querStr:str,fillStr:str):
    page.locator("//label",has_text=querStr).locator("//parent::div/following-sibling::div[1]//input").click()
    page.locator("//label",has_text=querStr).locator("//parent::div/following-sibling::div[1]//input").fill(fillStr)
    page.locator("//label",has_text=querStr).click()

def fillLalbel_input_2(page:Page,querStr:str,fillStr:str):
    page.locator("//label",has_text=querStr).locator("//parent::div/parent::div/parent::div/following-sibling::div[1]//input").click()
    page.locator("//label",has_text=querStr).locator("//parent::div/parent::div/parent::div/following-sibling::div[1]//input").fill(fillStr)
    page.locator("//label",has_text=querStr).click()

def getLalbel_inputValue(page:Page,querStr:str):
    return getVisible(page,page.locator("//label",has_text=querStr).locator("//parent::div/following-sibling::div[1]//input")).input_value()
def getLalbel_inputText(page:Page,querStr:str):
    return getVisible(page,page.locator("//label",has_text=querStr).locator("//parent::div/following-sibling::div[1]")).text_content()

def chooseIfPaper(page:Page,ifPaper:bool=False):
    s="//label[contains(text(),'含原始纸质附件：')]/parent::div/following-sibling::div[1]/div/div/div"
    getVisible(page,s).click()
    if ifPaper:
        getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']").click()
    else:
        getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']/preceding-sibling::div[1]").click()

def clickLabel(page:Page,s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    page.locator(s).click()

def handle_dialog(dialog):
    """监听后处理"""
    dialog.dismiss()
def reload(page:Page):
    page.reload()
    page.on("dialog", handle_dialog)
    try:
        page.locator("//span[text()='首页' and @class='tag']").click()
        page.locator("//label[text()='我的单据']").click(timeout=15000)
    except:
        page.get_by_label("通知公告").get_by_label("Close", exact=True).click()

def fillReason(page:Page,fillStr:str):
    page.locator("//input[@placeholder='事由不能超过50字']").click()
    page.keyboard.type(fillStr) 