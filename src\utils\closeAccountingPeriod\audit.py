# Read the contents of the txt file
import src.utils.DB.outputSQL as sql
import pandas as pd
import duckdb
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import src.utils.cscec as cscec
import src.base.settings as settings
import src.utils.Browser.Browser as Browser
import src.utils.DB.mainDB as mainDB
import src.utils.fileui as fileui
import datetime


def getincompatibleAccountDf(df:pd.DataFrame):
    df1=df[(df["总账科目长文本"]=="合同负债\工程款\已结算未完工") & (df["期末方向"]!="平")]
    df2=df[(df["总账科目长文本"]=="合同资产\工程款（已完工未结算）") & (df["期末方向"]!="平")]
    intersection = pd.merge(df1, df2, on='利润中心',how='inner')
    result = pd.concat([df1, df2])
    result0 = result[result["利润中心"].isin(intersection["利润中心"])]   
    df10=df[(df["总账科目长文本"]=="应收账款\待转销项税额\一般计税") & (df["期末方向"]!="平")]
    df20=df[(df["总账科目长文本"]=="应交税费\增值税\待转销项税额\一般计税") & (df["期末方向"]!="平")]
    intersection2 = pd.merge(df10, df20, on='利润中心',how='inner')
    result = pd.concat([df10, df20])
    result1 = result[result["利润中心"].isin(intersection2["利润中心"])] 
    return pd.concat([result0,result1])

def getaccountDirection(df:pd.DataFrame,dfSubjectOverview:pd.DataFrame): #arr科目方向数据方向，理论还要将不全的科目体现出来
    df3=pd.merge(df,dfSubjectOverview,how="left",on="总账科目长文本")
    df3['科目结果'] = df3.apply(lambda row: '无误' if str(row['期末方向']) in str(row['科目方向']) else '有误', axis=1)
    df3=df3[df3['科目结果']=='有误']
    return df3

def getoffsetCheck(df:pd.DataFrame,dfBasedata:pd.DataFrame):#arr为基础数据
    conn = duckdb.connect(':memory:')
    df2=conn.execute(sql.抵销检查).df()
    return df2

def contractCheck(con:duckdb.DuckDBPyConnection):#arr为基础数据
    df=con.execute("select *,发票金额-已付金额 as 发票检查  from 一体化合同台账 where (合同类型 not like '%承包%' and 合同类型 not like '%协议书%' and 税率 like '进项%') and (拖欠款<-0.01 or 发票检查<-0.01) ").df()
    return df

    
def main2(startDate=None,endDate=None,source="本地"):
    
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
    if source=="本地":
        queryL=sql.科目余额表
        queryL=queryL.replace('期初日期留',startDate)
        queryL=queryL.replace('期末日期留',endDate) 
        df=con.execute(queryL).df()
    else:
        df=con.execute("select * from 科目余额表第二期").df()
    accountTable=con.execute("select * from 科目对照").df()
    mdTable=con.execute("select * from 主数据").df()
    con.close()
    df1=getaccountDirection(df,accountTable)
    df2=getincompatibleAccountDf(df)
    df3=getoffsetCheck(df,mdTable)

    with pd.ExcelWriter(settings.PATH_QUERY+"/稽核结果.xlsx", engine='openpyxl') as writer:
        df1.to_excel(writer, sheet_name='科目余额方向检查', index=False)
        df2.to_excel(writer, sheet_name='不兼容科目检查', index=False)
        df3.to_excel(writer, sheet_name='抵销检查', index=False)

def main():
    con=mainDB.mainDB().conn
    df=con.execute("select * from 科目余额表第二期 where 总账科目长文本 not like '主营业务%' and 总账科目长文本 not like '税金%'").df()
    accountTable=con.execute("select * from 科目对照").df()
    mdTable=con.execute("select * from 主数据").df()
    df1=getaccountDirection(df,accountTable)
    df2=getincompatibleAccountDf(df)
    df3=getoffsetCheck(df,mdTable)
    df4=contractCheck(con)
    #计算半年前日期
    endDate=datetime.datetime.now()
    startDate=endDate - datetime.timedelta(days=180)
    df5=con.execute(f"select * from 预付账款台账 where 预付账款余额>0.01 and 申请时间 > '{startDate.strftime('%Y-%m-%d')}'").df()
    df6=con.execute(f"select * from 保证金台账 where 剩余金额>0.01 and 截止日期 < '{endDate.strftime('%Y-%m-%d')}'").df()
    con.close()
    #汇总数量
    from src.utils.DB.configDB import configDB
    unitMapping=configDB().unitMapping
    if len(unitMapping)>0:
        unitdf=pd.DataFrame(columns=["sourceUnitName","sourceUnitCode","targetUnitName"],data=unitMapping)
        c1=df1.merge(unitdf,left_on="利润中心组名称",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c1["检查类型"]="科目余额方向检查"
        c2=df2.merge(unitdf,left_on="利润中心组名称",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c2["检查类型"]="不兼容科目检查"
        c3=df3.merge(unitdf,left_on="利润中心组描述",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c3["检查类型"]="抵销检查"
        c4=df4.merge(unitdf,left_on="组织机构名称",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c4["检查类型"]="合同台账"
        c5=df5.merge(unitdf,left_on="单位名称",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c5["检查类型"]="预付账款台账"
        c6=df6.merge(unitdf,left_on="组织机构名称",right_on="sourceUnitName",how="left").groupby("targetUnitName")["targetUnitName"].count().reset_index(name="数量")
        c6["检查类型"]="保证金台账"

        dfall=pd.concat([c1,c2,c3,c4,c5,c6])
        #数据透视
        dfallpivot=dfall.pivot_table(values='数量', index='targetUnitName', columns='检查类型', aggfunc='sum').reset_index()

    path=fileui.select_directory()
    with pd.ExcelWriter(path+"/稽核结果.xlsx", engine='openpyxl') as writer:
        if len(unitMapping)>0:
            dfallpivot.to_excel(writer, sheet_name='稽核汇总', index=False)
        df1.to_excel(writer, sheet_name='科目余额方向检查', index=False)
        df2.to_excel(writer, sheet_name='不兼容科目检查', index=False)
        df3.to_excel(writer, sheet_name='抵销检查', index=False)
        df4.to_excel(writer, sheet_name='合同台账', index=False)
        df5.to_excel(writer, sheet_name='预付台账', index=False)
        df6.to_excel(writer, sheet_name='押金台账', index=False)
    print(f"导出在{path}下面，文件名为稽核结果")