import requests
import sys
import os
import src.base.settings as setting

class update:
    def __init__(self):
        pass
    @staticmethod
    def downloadFile():
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip"
        path=os.path.dirname(sys.executable)+"/ftools.zip"
        r = requests.get(url)
        with open(path,'wb') as f:
            f.write(r.content)

        #便利指定目录删除以index开头的文件
        if not os.path.exists(setting.PATH_INTERNAL+"/web/assets"):
            os.makedirs(setting.PATH_INTERNAL+"/web/assets")
        for file in os.listdir(setting.PATH_INTERNAL+"/web/assets"):
            if file.startswith("index"):
                os.remove(setting.PATH_INTERNAL+"/web/assets/"+file)
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.zip" #更新web
        path=setting.PATH_INTERNAL+"/web/web.zip"
        r = requests.get(url)
        with open(path,'wb') as f:
            f.write(r.content)
        import zipfile
        with zipfile.ZipFile(path, 'r') as z:
            z.extractall(setting.PATH_INTERNAL+"/web")
        os.remove(path)

        #更新表格缓存
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.txt" #更新web
        r = requests.get(url)
        #以utf-8编码读取内容
        r = r.content.decode('utf-8')
        from src.utils.DB.midIntrSQLiteDB import webDB
        db=webDB()
        db.writeData("表格缓存", [r,"最新模板"])
        db.close()


        if False:
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web_assets.zip" #更新web，修改到系统维护里面啦
            path=setting.PATH_INTERNAL+"/web/web_assets.zip"
            r = requests.get(url)
            with open(path,'wb') as f:
                f.write(r.content)
            import zipfile
            with zipfile.ZipFile(path, 'r') as z:
                z.extractall(setting.PATH_INTERNAL+"/web/assets")
            os.remove(path)

        print("更新完成，请重启")
