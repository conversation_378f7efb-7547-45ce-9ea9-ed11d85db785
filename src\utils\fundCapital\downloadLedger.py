import os
import src.base.settings as settings
import src.utils.Browser.Browser as browser
import time
import src.utils.cscec as cscec
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import pandas as pd
import src.utils.DB.mainDB as mainDB
from src.utils.DB.configDB import configDB
import openpyxl

def main(ifresume:str):
    def getUsedListByPath(path:str):
        wb = openpyxl.load_workbook(path)
        sheet = wb.active
        useList = []
        for row in sheet.iter_rows(min_row=1, values_only=True):
            useList.append(list(row))
        return useList
    if ifresume=="yes":
        excludeList_advancePayment=mainDB.queryDistctinctColumn("预付账款台账","单位名称")
        excludeList_margin=mainDB.queryDistctinctColumn("保证金台账","组织机构")
    circularList=configDB().financialIntegrationUnits
    B=browser.myBrowser("cscec")
    page=B.page
    print("启动浏览器成功")
    advancePaymentList=[]
    marginList=[]
    try:
        for i in range(len(circularList)):
            organizationCode=circularList[i][0]  #获取需填入编码
            organizationName=circularList[i][1]  #获取需填入名称
            print("当前组织机构为："+circularList[i][1])
            if ifresume=="yes" and (organizationName,) in excludeList_advancePayment:
                    print("该组织机构已导出过预付款，跳过")
            else:
                cscec.toFunction(page,"报账系统","台账管理","预付账款台账")
                cscec.Lablechoose(page,"单位","请输入查询关键字","帮助字典",organizationCode)
                js1 = 'jsneed=>{jsneed.removeAttribute("readonly");}'
                jsneed=cscec.getVisible(page,"//input[@id='FormDateField1-input']")
                jsneed.evaluate(js1)
                jsneed.fill(f"2019-08-31") #默认设置为19年831过账日期
                #监听post请求完成
                #with page.expect_request(lambda request:request.url == "https://fip.cscec.com/FIService/images/csc/newIcon/icon_query_w.svg" and request.method == "GET",timeout=180000) as req:
                cscec.getVisible(page,"//span[text()='查询']").click()
                time.sleep(1)
                #cscec.getVisible(page,"//td/div/div[text()='1']")
                cscec.getVisible(page,"//span[text()='导出']").click(timeout=300000)
                try:
                    page.locator(f"//*[contains(text(),'系统提示')]/parent::div/parent::div/parent::div//*[text()='确定']").click(timeout=5000)
                except:
                    with page.expect_download(timeout=180000) as download_info:
                        cscec.clickDigalog(page,"导出Excel","导出")
                        download=download_info.value
                        download.save_as(path=settings.PATH_INTERNAL+"/导出数据/临时文件3.xlsx")
                    cscec.closeTab(page)
                    temporaryList=getUsedListByPath(settings.PATH_INTERNAL+"/导出数据/临时文件3.xlsx")
                    os.remove(settings.PATH_INTERNAL+"/导出数据/临时文件3.xlsx")
                    if len(advancePaymentList)>0:
                        advancePaymentList=advancePaymentList+temporaryList[1:]
                    else:
                        advancePaymentList=advancePaymentList+temporaryList
                    #预付款台账导出完成
            if ifresume=="yes" and (organizationCode,) in excludeList_margin:
                    print("该组织机构已导出过保证金，跳过")
            else:
                cscec.toFunction(page,"报账系统","台账管理","应收保证金（押金）台账V2.0")
                cscec.Lablechoose(page,"组织机构：","请输入查询关键字","帮助字典",organizationCode)
                js1 = 'jsneed=>{jsneed.removeAttribute("readonly");}'
                jsneed=cscec.getVisible(page,"//input[@id='FormDateField1-input']")
                jsneed.evaluate(js1)
                jsneed.fill(f"2019-08-31")
                #with page.expect_request(lambda request:request.url == "https://fip.cscec.com/FIService/images/csc/newIcon/icon_query_w.svg" and request.method == "GET",timeout=180000)  as req:
                cscec.getVisible(page,"//span[text()='查询']").click()
                #cscec.getVisible(page,"//td/div/div[text()='1']")
                time.sleep(1)
                cscec.getVisible(page,"//span[text()='导出']").click(timeout=300000)
                try:
                    page.locator(f"//*[contains(text(),'系统提示')]/parent::div/parent::div/parent::div//*[text()='确定']").click(timeout=5000)
                except:
                    with page.expect_download(timeout=180000) as download_info:
                        cscec.clickDigalog(page,"导出Excel","导出")
                        download=download_info.value
                        download.save_as(path=settings.PATH_INTERNAL+"/导出数据/临时文件2.xlsx")
                    cscec.closeTab(page)
                    temporaryList=getUsedListByPath(settings.PATH_INTERNAL+"/导出数据/临时文件2.xlsx")
                    os.remove(settings.PATH_INTERNAL+"/导出数据/临时文件2.xlsx")
                    if len(marginList)>0:
                        marginList=marginList+temporaryList[1:]
                    else:
                        marginList=temporaryList+marginList
    except Exception as e:
        print("失败一次"+str(e))
    if len(marginList)>0:
        marginListdf=pd.DataFrame(marginList[1:],columns=marginList[0])
        if ifresume=="yes":
            mainDB.updateDataframeToDB(marginListdf,"保证金台账") #写入数据库
        else:
            mainDB.wirteDataframeToDB(marginListdf,"保证金台账") #写入数据库
    
    if len(advancePaymentList)>0:
        advancePaymentListdf=pd.DataFrame(advancePaymentList[1:],columns=advancePaymentList[0])
        if ifresume=="yes":
            mainDB.updateDataframeToDB(advancePaymentListdf,"预付账款台账") #写入数据库
        else:
            mainDB.wirteDataframeToDB(advancePaymentListdf,"预付账款台账") #写入数据库

